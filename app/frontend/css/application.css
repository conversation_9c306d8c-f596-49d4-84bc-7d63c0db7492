/* 
  NOTE: Tailwind CSS is loaded through Vite via main.css
  This file contains legacy styles converted from SCSS.
*/

/* Týmbox Desktop Layout - Core Styles */
/* Base Variables replaced with hex values */

/* Reset & Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Reset button */
button, .button_to, button_to {
  /* appearance: none; */
  /* -webkit-appearance: none; */
  line-height: 1;
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  cursor: pointer;
}

body {
  /* font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif; */
  font-family: 'Inter', sans-serif;
  font-optical-sizing: auto;
  font-weight: 300;
  font-style: normal;
  color: #1f2937; /* gray-800 */
  line-height: 1.5;
  background-color: #f9fafb; /* gray-50 */
}

a {
  text-decoration: none;
  color: inherit;
}

.brand-logo {
  height: 46px;
}

/* Layout Classes */
.app-container {
  display: flex;
  flex-direction: column; 
  min-height: 100vh;
  overflow-x: hidden;
}

@media (min-width: 768px) {
  .app-container {
    flex-direction: row; 
  }
}

/* Sidebar */
/* Update sidebar for mobile-first */
.sidebar {
  width: 100%;
  height: 100vh;  
  position: fixed;
  z-index: 50;
  background-color: white;
  transform: translateX(-100%); 
  transition: transform 0.3s ease, width 0.3s ease;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar.collapsed {
  width: 4rem;
}

@media (min-width: 768px) {
  .sidebar {
    transform: translateX(0);
    width: 17rem;
    height: 100vh;
    position: relative;
    border-right: 1px solid #e5e7eb; /* gray-200 */
  }
  .sidebar.collapsed {
    width: 4rem;
  }
}

.sidebar-header {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f3f4f6; /* gray-100 */
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  min-height: 0; 
}

.sidebar-footer {
  border-top: 1px solid #f3f4f6; /* gray-100 */
  padding: 1rem;
}

.sidebar-divider {
  height: 1px;
  background-color: #f3f4f6; /* gray-100 */
  margin: 0.5rem 0;
}

/* Time tracking quick-access */
.time-tracker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6; /* gray-100 */
}

.time-tracker-collapsed {
  justify-content: center;
}

.time-toggle {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.time-toggle-active {
  background-color: #22c55e; /* success */
  color: white;
}

.time-toggle-inactive {
  background-color: #e5e7eb; /* gray-200 */
  color: #6b7280; /* gray-500 */
}

/* Navigation */
.nav-section {
  margin-bottom: 1.5rem;
}

.nav-section-title {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280; /* gray-500 */
  padding: 0 1.25rem;
  margin-bottom: 0.5rem;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.625rem 0.75rem;
  margin: 0 0.5rem 0.25rem 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  color: #4b5563; /* gray-600 */
  transition: all 0.15s ease;
}

.nav-item-icon {
  color: currentColor;
  margin-right: 0.75rem;
}

.nav-item-text {
  font-size: 0.875rem;
  font-weight: 500;
}

.nav-item-active {
  background-color: #dbeafe; /* primary-light */
  color: #3b82f6; /* primary */
}

.nav-item:hover:not(.nav-item-active) {
  background-color: #f3f4f6; /* gray-100 */
}

.nav-item-collapsed {
  justify-content: center;
  padding: 0.625rem;
  margin: 0 0.5rem 0.25rem 0.5rem;
}

.nav-item-collapsed .nav-item-icon {
    margin-right: 0;
}


.nav-badge {
  font-size: 0.7rem;
  font-weight: 500;
  padding: 0.1rem 0.5rem;
  border-radius: 0.25rem;
  background-color: #dbeafe; /* primary-light */
  color: #3b82f6; /* primary */
  margin-right: 0.5rem;
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-top: 4rem; 
}

@media (min-width: 768px) {
  .main-content {
    margin-top: 0;
  }
}

/* Top Bar (Commented out section omitted) */

/* Update company selector for mobile */
.company-selector {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.375rem;
  padding: 0.375rem 0.5rem; 
  font-weight: 500;
  color: #374151; /* gray-700 */
  font-size: 0.875rem;
  gap: 0.25rem;
}

@media (min-width: 768px) {
  .company-selector {
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    gap: 0.5rem;
  }
}

.plan-badge {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  background-color: #dbeafe; /* primary-light */
  color: #3b82f6; /* primary */
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.topbar-actions .topbar-btn {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  color: #4b5563; /* gray-600 */
  cursor: pointer;
  position: relative;
}

.topbar-actions .topbar-btn:hover {
    background-color: #f3f4f6; /* gray-100 */
}

.topbar-actions .topbar-btn .notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    width: 1rem;
    height: 1rem;
    border-radius: 9999px;
    background-color: #ef4444; /* danger */
    color: white;
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}


/* Content Area */
/* Update content area for mobile-first */
.content-area {
  flex: 1;
  overflow-y: auto;
  /* margin-top: 64px; */
  padding: 0;
}

@media (min-width: 768px) {
  .content-area {
    margin-top: 0;
    padding: 1.5rem;
  }
}

/* CUSTOM GRID LAYOUT */
.content-area .grid {
  display: grid;
  /* gap: 1rem; */
}

@media (min-width: 768px) {
  .content-area .grid {
    /* gap: 1.5rem; */
  }
}

.content-area .grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 1111px) { 
  .content-area .grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

.content-area .grid-cols-3 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
  
@media (min-width: 1165px) { 
  .content-area .grid-cols-3 { 
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
@media (min-width: 1536px) {
  .content-area .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}


.content-area .col-span-1 {
  grid-column: span 1 / span 1;
}

.content-area .col-span-2 {
  grid-column: span 1 / span 1;
}

@media (min-width: 768px) { 
  .content-area .col-span-2 {
    grid-column: span 2 / span 2;
  }
}

.content-area .card {
  /* min-width: 380px; */
  max-width: 500px;
}


/* Cards & Panels */
/* Update card styles for mobile */
.card {
  background-color: white;
  /* border-radius: 0.5rem; */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
  .card {
    /* border-radius: 0.75rem; */
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #f3f4f6;
  gap: 0.5rem;
  padding: 0.5rem;
  /* margin-bottom: 0.5rem; */
}

@media (min-width: 768px) {
  .card-header {
    gap: 1rem;
  }
}

.card-header h2 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937; /* gray-800 */
    margin: 0;
}

@media (min-width: 768px) {
  .card-header h2 {
    font-size: 1.125rem;
  }
}

.card-header span {
    white-space: nowrap;
    flex-shrink: 0;
}


.card-content {
  padding: 0.5rem;
}

@media (min-width: 768px) {
  .card-content {
    padding: 1rem;
  }
}

.card-footer {
  padding: 0.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

@media (min-width: 768px) {
  .card-footer {
      padding: 1rem;
  }
}


.panel {
  padding: 1rem;
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #f3f4f6; /* gray-100 */
  margin-bottom: 1rem;
  transition: background-color 0.2s ease;
}

.panel:hover {
  background-color: #f9fafb; /* gray-50 */
}

/* Status indicators */
.status-indicator {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #d1d5db; /* gray-300 */
}

.status-indicator-active {
  background-color: #22c55e; /* success */
}

.status-indicator-away {
  background-color: #eab308; /* warning */
}


/* Badges */
.badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-success {
  background-color: #dcfce7; /* success-light */
  color: #16a34a; /* darken(success, 10%) approx */
}

.badge-warning {
  background-color: #fef9c3; /* warning-light */
  color: #ca8a04; /* darken(warning, 10%) approx */
}

.badge-danger {
  background-color: #fee2e2; /* danger-light */
  color: #dc2626; /* darken(danger, 10%) approx */
}

.badge-gray {
  background-color: #f3f4f6; /* gray-100 */
  color: #1f2937; /* gray-800 */
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background-color: #3b82f6; /* primary */
  color: white;
}

.btn-primary:hover {
    background-color: #2563eb; /* primary-hover */
}


.btn-secondary {
  background-color: #f3f4f6; /* gray-100 */
  color: #4b5563; /* gray-600 */
}

.btn-secondary:hover {
    background-color: #e5e7eb; /* gray-200 */
}


.btn-outline {
  border: 1px solid #d1d5db; /* gray-300 */
  background-color: transparent;
  color: #374151; /* gray-700 */
}

.btn-outline:hover {
    background-color: #f9fafb; /* gray-50 */
}
.btn-outline:disabled {
    background-color: #e5e7eb; /* gray-200 */
    color: #9ca3af; /* gray-400 */
    cursor: not-allowed;
}


.btn-light {
  background-color: #dbeafe; /* primary-light */
  color: #3b82f6; /* primary */
}

.btn-light:hover {
    background-color: #bfdbfe; /* darken(primary-light, 5%) approx */
}

.btn-danger-light {
  background-color: #fee2e2; /* danger-light */
  color: #ef4444; /* danger */
}

.btn-danger-light:hover {
    background-color: #fecaca; /* darken(danger-light, 5%) approx */
}


.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}



/* Add mobile menu toggle button */
.mobile-menu-toggle {
  display: block;
  padding: 0.5rem;
  margin-right: 0.5rem;
  background: none;
  border: none;
  cursor: pointer;
  color: #4b5563; /* gray-600 */
}

.mobile-menu-toggle:hover {
   color: #1f2937; /* gray-800 */
}

@media (min-width: 768px) {
  .mobile-menu-toggle {
    display: none;
  }
}

/* Update the mobile overlay style */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 45; /* Should be below the sidebar (z-index: 50) */
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.mobile-overlay.active { 
    opacity: 1;
    visibility: visible;
}

@media (min-width: 768px) {
  .mobile-overlay {
    display: none;
  }
}

/* Modal styles for application-wide use */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9990; 
}

.modal-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 550px; 
  max-height: 90vh;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.8rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  line-height: 1;
}

.close-btn:hover {
  color: #333;
}

.central-modal-content {
  padding: 20px;
  overflow-y: auto; 
  flex-grow: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
  padding: 15px 20px;
  border-top: 1px solid #eee;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

/* NEW FORM STYLES */
.form-section {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151; /* gray-700 */
}

.form-input,
.form-textarea,
.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #111827; /* gray-900 */
  background-color: white;
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: #3b82f6; /* primary */
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2); /* primary with alpha */
}

.form-input::placeholder,
.form-textarea::placeholder,
.form-select::placeholder {
  color: #9ca3af; /* gray-400 */
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.form-checkbox-group {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.form-checkbox {
  height: 1rem;
  width: 1rem;
  border-radius: 0.25rem;
  border: 1px solid #d1d5db; /* gray-300 */
  color: #3b82f6; /* primary */
  margin-right: 0.5rem;
  cursor: pointer;
  flex-shrink: 0;
}

.form-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2); /* primary with alpha */
}

.form-checkbox:checked {
  background-color: #3b82f6; /* primary */
  border-color: #3b82f6; /* primary */
}

.form-checkbox-label {
  font-size: 0.875rem;
  color: #374151; /* gray-700 */
  cursor: pointer;
}

/* Add styles for radio buttons */
.form-radio-group {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem; /* Consistent spacing */
}

.form-radio {
  /* Appearance might vary slightly, aiming for consistency */
  appearance: none;
  -webkit-appearance: none;
  height: 1rem;
  width: 1rem;
  border-radius: 50%; /* Radio buttons are circular */
  border: 1px solid #d1d5db; /* gray-300 */
  margin-right: 0.5rem;
  cursor: pointer;
  flex-shrink: 0;
  position: relative; /* For the inner circle */
}

.form-radio:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2); /* primary with alpha */
}

.form-radio:checked {
  border-color: #3b82f6; /* primary */
}
/* Add inner circle */
.form-radio:checked::after {
    content: "";
    display: block;
    width: 0.5rem; /* Size of the inner dot */
    height: 0.5rem;
    border-radius: 50%;
    background-color: #3b82f6; /* primary */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.form-radio-label {
  font-size: 0.875rem;
  color: #374151; /* gray-700 */
  cursor: pointer;
}
/* End radio button styles */

.form-file-input {
  display: block;
  width: 100%;
  font-size: 0.875rem;
  color: #374151; /* gray-700 */
}

.form-file-input::file-selector-button {
  padding: 0.375rem 0.75rem;
  margin-right: 0.75rem;
  border: 1px solid #d1d5db; /* gray-300 */
  border-radius: 0.375rem;
  background-color: white;
  color: #374151; /* gray-700 */
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.form-file-input::file-selector-button:hover {
    background-color: #f9fafb; /* gray-50 */
}


.form-error-message {
  color: #ef4444; /* danger */
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Manual copy of .btn and .btn-primary styles for .form-button */
.form-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background-color: #3b82f6; /* primary */
  color: white;
}
.form-button:hover {
  background-color: #2563eb; /* primary-hover */
}

/* Manual copy of .btn and .btn-secondary styles for .form-button-secondary */
.form-button-secondary {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background-color: #f3f4f6; /* gray-100 */
  color: #4b5563; /* gray-600 */
}
.form-button-secondary:hover {
  background-color: #e5e7eb; /* gray-200 */
}

/* Manual copy of .btn and .btn-outline styles for .form-button-outline */
.form-button-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #d1d5db; /* gray-300 */
  background-color: transparent;
  color: #374151; /* gray-700 */
}
.form-button-outline:hover {
  background-color: #f9fafb; /* gray-50 */
}
.form-button-outline:disabled {
  background-color: #e5e7eb; /* gray-200 */
  color: #9ca3af; /* gray-400 */
  cursor: not-allowed;
}

/* Style for small hint text below form inputs */
.form-hint {
  display: block; /* Ensure it takes its own line if needed */
  font-size: 0.75rem; /* Smaller text */
  color: #6b7280; /* gray-500 */
  margin-top: 0.25rem; /* Space above */
}

/* END NEW FORM STYLES */

/* Form Error Styling */
.form-error-container {
  background-color: #fee2e2; /* danger-light */
  border: 1px solid #fecaca; /* darken(danger-light, 10%) approx */
  border-radius: 0.375rem; /* Match form input radius */
  padding: 0.75rem; /* Add some padding */
}

.form-error-item {
  color: #dc2626; /* darken(danger, 10%) approx */
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.form-error-item:last-child {
  margin-bottom: 0;
}

/* NEW Content Panel Class */
.content-panel {
  background-color: white;
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb; /* gray-200 */
  padding: 1rem; 
  margin-bottom: 1.5rem;
}

/* For the public sites */
.public-layout {
  min-height: 100vh;
  background: white;
}

.public-layout .main-content {
  max-width: 56rem;
  min-height: calc(100vh - 99px);
  margin: 0 auto;
  padding: 1rem 1rem 0 1rem;
}
  
@media (max-width: 768px) {
  .public-layout .main-content {
    padding: 2rem 0.5rem;
  }
}

.public-layout .footer {
  background-color: #f8f9fa;
  padding: 1rem 1rem;
  margin-top: 3rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

/* Commented out .footer styles omitted */

.public-layout .beta-tag {
  background-color: #3b82f6; /* primary */
  color: white;
  font-size: 0.625rem;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  vertical-align: super;
  margin-left: 0.25rem;
} 