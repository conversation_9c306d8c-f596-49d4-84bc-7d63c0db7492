<template>
  <div class="employee-sidebar">
    <div class="sidebar-header">
      <h3 class="sidebar-title">{{ $t('employees', '<PERSON><PERSON><PERSON><PERSON>nanci') }}</h3>
    </div>
    
    <div v-if="loading" class="sidebar-loading">
      <div class="loading-spinner"></div>
      <span>{{ $t('loading', 'Načítání...') }}</span>
    </div>
    
    <div v-else-if="error" class="sidebar-error">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchEmployees" class="retry-button">
        {{ $t('retry', 'Zkusit znovu') }}
      </button>
    </div>
    
    <div v-else class="employee-list">
      <div 
        v-for="employee in employees" 
        :key="employee.id"
        class="employee-item"
        :class="{ 'employee-item--selected': selectedEmployeeId === employee.id }"
        @click="selectEmployee(employee)"
      >
        <div class="employee-info">
          <div class="employee-name">{{ employee.name }}</div>
          <div class="employee-meta">
            <span v-if="employee.working" class="status-indicator status-working">
              {{ $t('working', 'Pracuje') }}
            </span>
            <span v-else class="status-indicator status-not-working">
              {{ $t('not_working', 'Nepracuje') }}
            </span>
          </div>
        </div>
        <div v-if="selectedEmployeeId === employee.id" class="selected-indicator">
          <Check :size="16" />
        </div>
      </div>
      
      <div v-if="employees.length === 0" class="no-employees">
        <p>{{ $t('no_employees', 'Žádní zaměstnanci') }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Check } from 'lucide-vue-next';
import { sendFlashMessage } from '../utils/flashMessage';

export default {
  name: 'EmployeeSidebar',
  components: {
    Check
  },
  props: {
    selectedEmployeeId: {
      type: [String, Number],
      default: null
    }
  },
  emits: ['employee-selected'],
  data() {
    return {
      employees: [],
      loading: true,
      error: null
    };
  },
  methods: {
    async fetchEmployees() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await axios.get('/api/v1/employees');
        this.employees = response.data;
        
        // Auto-select first employee if none selected
        if (this.employees.length > 0 && !this.selectedEmployeeId) {
          this.selectEmployee(this.employees[0]);
        }
      } catch (error) {
        console.error('Error fetching employees:', error);
        this.error = this.$t('employees.fetch_error', 'Nepodařilo se načíst seznam zaměstnanců');
        sendFlashMessage(this.error, 'error');
      } finally {
        this.loading = false;
      }
    },
    
    selectEmployee(employee) {
      this.$emit('employee-selected', employee);
    }
  },
  
  mounted() {
    this.fetchEmployees();
  }
};
</script>

<style scoped>
.employee-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e5e7eb;
  height: 100vh;
  overflow-y: auto;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.sidebar-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.sidebar-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6b7280;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.sidebar-error {
  padding: 1rem;
  text-align: center;
}

.error-message {
  color: #dc2626;
  margin-bottom: 1rem;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.retry-button:hover {
  background: #2563eb;
}

.employee-list {
  padding: 0.5rem 0;
}

.employee-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.15s ease;
}

.employee-item:hover {
  background: #f9fafb;
}

.employee-item--selected {
  background: #eff6ff;
  border-right: 3px solid #3b82f6;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.25rem;
}

.employee-meta {
  font-size: 0.75rem;
}

.status-indicator {
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-working {
  background: #dcfce7;
  color: #166534;
}

.status-not-working {
  background: #f3f4f6;
  color: #6b7280;
}

.selected-indicator {
  color: #3b82f6;
  margin-left: 0.5rem;
}

.no-employees {
  padding: 2rem 1rem;
  text-align: center;
  color: #6b7280;
}

@media (max-width: 768px) {
  .employee-sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }
}
</style>
