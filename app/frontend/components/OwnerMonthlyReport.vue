<template>
  <div class="owner-monthly-report">
    <EmployeeSidebar 
      :selected-employee-id="selectedEmployeeId"
      @employee-selected="handleEmployeeSelected"
    />
    
    <div class="report-content">
      <div v-if="isLoading" class="time-tracking-loading">
        <Clock :size="20" class="time-tracking-loading-icon" />
      </div>
      
      <template v-else-if="selectedEmployee">
        <div class="report">
          <div class="report-header p-2">
            <h2 class="text-xl md:text-2xl font-semibold text-gray-800">
              {{ $t('attendance_report.title', 'Docházka') }} - {{ selectedEmployee.name }}
              <br>
              <span class="text-lg text-gray-600">{{ currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1) }}</span>
            </h2>
            <div>
              <select :value="getCurrentMonthValue()" @change="onMonthSelect($event.target.value)" class="month-selector">
                <option value="" disabled selected>{{ $t('select_month', 'Vyberte mě<PERSON>íc') }}</option>
                <option v-for="month in availableMonths" 
                        :key="month.value" 
                        :value="month.value">
                  {{ month.label }}
                </option>
              </select>
            </div>
          </div>

          <div class="content-panel">
            <div class="flex items-start gap-2 p-1 rounded text-sm text-gray-600">
              <div class="flex items-center justify-center w-6 h-6">
                <Info size="16" />
              </div>
              <p>
                {{ $t('owner_report.help_text', 'Přehled docházky zaměstnance. Data jsou pouze pro čtení.') }}
              </p>
            </div>
          </div>
        
          <div class="report-table">
            <div class="report-row report-head">
              <div class="report-cell">{{ $t('day', 'Den') }}</div>
              <div class="report-cell">{{ $t('start', 'Začátek') }}</div>
              <div class="report-cell">{{ $t('end', 'Konec') }}</div>
              <div class="report-cell">{{ $t('break', 'Pauza') }}</div>
              <div class="report-cell">{{ $t('end', 'Konec') }}</div>
              <div class="report-cell">{{ $t('hours', 'Hodin') }}</div>
            </div>

            <div 
              v-for="day in days" 
              :key="day.day" 
              class="report-row"
              :class="{
                'is-weekend': day.is_weekend,
                'is-holiday': day.is_holiday
              }"
            >
              <div class="report-cell">
                <div class="day-info">
                  <div>
                    <span class="day-number">{{ day.day }}. </span>
                    <span class="weekday">{{ getWeekday(day.date) }}</span>
                  </div>
                </div>
              </div>
              
              <template v-if="day.log">
                <div class="report-cell">{{ formatTime(day.log.start_time) }}</div>
                <div class="report-cell">{{ formatTime(day.log.end_time) }}</div>
                <div class="report-cell">{{ day.log.break_start ? formatTime(day.log.break_start) : '-' }}</div>
                <div class="report-cell">{{ day.log.break_end ? formatTime(day.log.break_end) : '-' }}</div>
                <div class="report-cell">{{ formatDuration(day.log.duration) }}</div>
              </template>
              
              <template v-else-if="day.event">
                <div class="report-cell merge-cell">{{ day.event.description }}</div>
              </template>
              
              <template v-else>
                <div class="report-cell">-</div>
                <div class="report-cell">-</div>
                <div class="report-cell">-</div>
                <div class="report-cell">-</div>
                <div class="report-cell">-</div>
              </template>
            </div>

            <div class="report-row report-total">
              <div class="report-cell">{{ $t('total', 'Spolu') }}</div>
              <div class="report-cell"></div>
              <div class="report-cell"></div>
              <div class="report-cell"></div>
              <div class="report-cell"></div>
              <div class="report-cell">{{ formatDuration(totalHours) }}</div>
            </div>
          </div>

          <br/>
          <div class="content-panel">
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
              <div class="text-sm text-gray-700">{{ $t('attendance_report.days_worked', 'Odpracované dny') }}: {{ daysWorked }}</div>
              <div class="text-sm text-gray-700">{{ $t('event_type.vacation', 'Dovolená') }}: {{ eventCounts.vacation }}</div>
              <div class="text-sm text-gray-700">{{ $t('event_type.illness', 'Nemocenská') }}: {{ eventCounts.illness }}</div>
              <div class="text-sm text-gray-700">{{ $t('event_type.day_care', 'Návštěva lékaře') }}: {{ eventCounts.day_care }}</div>
              <div class="text-sm text-gray-700">{{ $t('event_type.family_sick', 'OČR') }}: {{ eventCounts.family_sick }}</div>
              <div class="text-sm text-gray-700">{{ $t('event_type.other', 'Jiné absence') }}: {{ eventCounts.other }}</div>
            </div>
          </div>
        </div>
      </template>
      
      <div v-else class="no-employee-selected">
        <div class="text-center py-8">
          <h3 class="text-lg font-medium text-gray-900 mb-2">
            {{ $t('owner_report.select_employee', 'Vyberte zaměstnance') }}
          </h3>
          <p class="text-gray-600">
            {{ $t('owner_report.select_employee_help', 'Vyberte zaměstnance ze seznamu vlevo pro zobrazení jeho docházky.') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { sendFlashMessage } from '../utils/flashMessage';
import { Clock, Info } from 'lucide-vue-next';
import EmployeeSidebar from './EmployeeSidebar.vue';

export default {
  name: 'OwnerMonthlyReport',
  components: {
    Clock, Info, EmployeeSidebar
  },
  data() {
    return {
      isLoading: false,
      selectedEmployee: null,
      selectedEmployeeId: null,
      days: [],
      currentMonth: '',
      totalHours: 0,
      daysWorked: 0,
      eventCounts: {
        vacation: 0,
        illness: 0,
        day_care: 0,
        family_sick: 0,
        other: 0
      },
      selectedDate: new Date(localStorage.getItem('lastViewedMonth') || new Date()),
      availableMonths: this.getLastTwelveMonths()
    };
  },
  methods: {
    getLastTwelveMonths() {
      const months = [];
      let date = new Date();
      
      for(let i = 0; i < 12; i++) {
        months.unshift({
          value: date.toISOString(),
          label: date.toLocaleString(this.$i18n.locale, { month: 'long', year: 'numeric' })
        });
        date = new Date(date.setMonth(date.getMonth() - 1));
      }
      return months;
    },

    onMonthSelect(value) {
      this.selectedDate = new Date(value);
      localStorage.setItem('lastViewedMonth', value);
      if (this.selectedEmployee) {
        this.fetchEmployeeReportData();
      }
    },

    getCurrentMonthValue() {
      return this.selectedDate.toISOString();
    },

    handleEmployeeSelected(employee) {
      this.selectedEmployee = employee;
      this.selectedEmployeeId = employee.id;
      this.fetchEmployeeReportData();
    },

    async fetchEmployeeReportData() {
      if (!this.selectedEmployee) return;
      
      this.isLoading = true;

      try {
        const date = this.selectedDate.toISOString().split('T')[0];
        const response = await axios.get('/daily_logs/fetch_employee_report_data', {
          params: { 
            date: date,
            contract_id: this.selectedEmployeeId
          }
        });
        
        if (response.data) {
          this.days = response.data.days;
          this.totalHours = response.data.total_hours;
          this.daysWorked = response.data.days_worked;
          this.eventCounts = response.data.event_counts;
          this.currentMonth = this.selectedDate.toLocaleString(this.$i18n.locale, { 
            month: 'long', 
            year: 'numeric' 
          });
        }
      } catch (error) {
        console.error('Error fetching employee report data:', error);
        sendFlashMessage(this.$t('owner_report.fetch_error', 'Nepodařilo se načíst data zaměstnance'), 'error');
      } finally {
        this.isLoading = false;
      }
    },

    getWeekday(dateString) {
      const weekdays = ['ne', 'po', 'út', 'st', 'čt', 'pá', 'so'];
      const date = new Date(dateString);
      return weekdays[date.getDay()];
    },

    formatTime(time) {
      if (!time) return '-';
      return new Date(time).toLocaleTimeString('cs-CZ', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    },

    formatDuration(seconds) {
      if (!seconds) return '-';
      const hours = seconds / 3600;
      return hours.toFixed(1);
    }
  }
};
</script>

<style scoped>
.owner-monthly-report {
  display: flex;
  height: 100vh;
  background: #f9fafb;
}

.report-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.time-tracking-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.time-tracking-loading-icon {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

.report {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.report-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 15px;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

select.month-selector {
  padding: 0.5rem;
  margin: 0;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.content-panel {
  padding: 1rem 1.5rem;
  background: #f8fafc;
  border-bottom: 1px solid #e5e7eb;
}

.report-table {
  width: 100%;
  border-collapse: collapse;
}

.report-row {
  display: grid;
  grid-template-columns: 1.1fr 0.9fr 0.9fr 0.9fr 0.9fr 0.5fr;
  border-bottom: 1px solid #eee;
  padding: 0 1.5rem;
}

.report-head {
  font-weight: 500;
  background: #f9fafb;
  border-bottom: 2px solid #e5e7eb;
}

.report-cell {
  padding: 0.75rem;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.merge-cell {
  grid-column: span 5;
  font-style: italic;
  color: #6b7280;
}

.report-total {
  font-weight: 500;
  border-top: 2px solid #e5e7eb;
  background: #f9fafb;
}

.day-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.day-number {
  font-size: 0.9rem;
  font-weight: 600;
}

.weekday {
  font-size: 0.9rem;
  color: #666;
}

.is-weekend {
  background: #B7EECB;
}

.is-holiday {
  background: #ffeeaa;
}

.no-employee-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .owner-monthly-report {
    flex-direction: column;
    height: auto;
  }

  .report-content {
    padding: 0.5rem;
  }

  .report-cell {
    padding: 0.5rem 0.2rem;
    font-size: 0.8rem;
  }

  .report-header {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>
